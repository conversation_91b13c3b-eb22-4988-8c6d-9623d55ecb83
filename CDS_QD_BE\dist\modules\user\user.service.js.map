{"version": 3, "sources": ["../../../src/modules/user/user.service.ts"], "sourcesContent": ["import { HttpException, Injectable } from '@nestjs/common';\nimport { DatabaseService } from '~/database/typeorm/database.service';\nimport { MediaService } from '~/modules/media/media.service';\nimport { TokenService, UtilService } from '~/shared/services';\nimport { CreateUserDto } from './dto/create-user.dto';\nimport { UpdateUserDto } from './dto/update-user.dto';\nimport { AssignRolesDto } from './dto/assign-roles.dto';\n\n@Injectable()\nexport class UserService {\n    constructor(\n        private readonly tokenService: TokenService,\n        private readonly utilService: UtilService,\n        private readonly mediaService: MediaService,\n        private readonly database: DatabaseService,\n    ) {}\n\n    async create(createUserDto: CreateUserDto) {\n        const { username, password, roleIds, ...rest } = createUserDto;\n        const { salt, hash } = this.tokenService.hashPassword(createUserDto.password);\n        const account = await this.database.account.save(\n            this.database.account.create({\n                username: createUserDto.username,\n                password: hash,\n                salt,\n            }),\n        );\n\n        if (!account) {\n            throw new HttpException('Cannot create account', 400);\n        }\n\n        const user = await this.database.user.save(this.database.user.create({ ...rest, accountId: account.id }));\n        if (!user) {\n            throw new HttpException('Cannot create user', 400);\n        }\n\n        // Handle multiple roles if provided\n        if (roleIds && roleIds.length > 0) {\n            const userRoles = roleIds.map((roleId) => ({\n                userId: user.id,\n                roleId,\n            }));\n            await this.database.usersRole.save(userRoles);\n        }\n\n        return {\n            data: {\n                account,\n                user: await this.findOne(user.id), // Return user with all roles\n            },\n        };\n    }\n\n    async findAll(queries: { page: number; perPage: number; search: string; sortBy: string }) {\n        const { builder, take, pagination } = this.utilService.getQueryBuilderAndPagination(this.database.user, queries);\n\n        if (!this.utilService.isEmpty(queries.search)) {\n            builder.andWhere('(entity.hoTen ILIKE :search OR entity.email ILIKE :search)', { search: `%${queries.search}%` });\n        }\n\n        // Include both single role and multiple roles\n        builder.leftJoinAndSelect('entity.role', 'role');\n        builder.leftJoinAndSelect('entity.userRoles', 'userRoles');\n        builder.leftJoinAndSelect('userRoles.role', 'userRole');\n        builder.leftJoinAndSelect('entity.avatar', 'avatar');\n        builder.select(['entity', 'role.id', 'role.name', 'userRoles', 'userRole.id', 'userRole.name', 'avatar.id']);\n\n        const [result, total] = await builder.getManyAndCount();\n        const totalPages = Math.ceil(total / take);\n        return {\n            data: result,\n            pagination: {\n                ...pagination,\n                totalRecords: total,\n                totalPages: totalPages,\n            },\n        };\n    }\n\n    findOne(id: number) {\n        return this.database.user.findOneUserWithAllRolesById(id);\n    }\n\n    // Get all roles for a user (both single and multiple)\n    async getAllUserRoles(userId: number) {\n        return this.database.user.getAllUserRoles(userId);\n    }\n\n    // Assign multiple roles to a user\n    async assignRolesToUser(userId: number, roleIds: number[]) {\n        const user = await this.database.user.findOneBy({ id: userId });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        // Remove existing user roles to avoid duplicates\n        await this.database.usersRole.delete({ userId });\n\n        // Add new roles\n        const userRoles = roleIds.map((roleId) => ({\n            userId,\n            roleId,\n        }));\n\n        await this.database.usersRole.save(userRoles);\n\n        return this.findOne(userId);\n    }\n\n    // Remove a specific role from user\n    async removeRoleFromUser(userId: number, roleId: number) {\n        const user = await this.database.user.findOneBy({ id: userId });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        await this.database.usersRole.delete({ userId, roleId });\n\n        return this.findOne(userId);\n    }\n\n    async update(id: number, updateUserDto: UpdateUserDto) {\n        const { username, password, roleIds, ...rest } = updateUserDto;\n        const user = await this.database.user.findOneBy({ id });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        if (password) {\n            const { salt, hash } = this.tokenService.hashPassword(updateUserDto.password);\n            this.database.account.update({ id: user.accountId }, { password: hash, salt });\n        }\n\n        // Handle multiple roles if provided\n        if (roleIds && roleIds.length > 0) {\n            // Remove existing user roles\n            await this.database.usersRole.delete({ userId: id });\n\n            // Add new roles\n            const userRoles = roleIds.map((roleId) => ({\n                userId: id,\n                roleId,\n            }));\n            await this.database.usersRole.save(userRoles);\n        }\n\n        await this.database.user.update({ id }, rest);\n        return this.findOne(id); // Return user with all roles\n    }\n\n    async remove(id: number) {\n        const user = await this.database.user.findOneBy({ id });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        // remove user\n        await this.database.user.delete({ id });\n        // remove account\n        await this.database.account.delete({ id: user.accountId });\n        // remove media\n        if (user.avatar?.id) {\n            await this.mediaService.remove(user.avatar.id);\n        }\n\n        return true;\n    }\n}\n"], "names": ["UserService", "create", "createUserDto", "username", "password", "roleIds", "rest", "salt", "hash", "tokenService", "hashPassword", "account", "database", "save", "HttpException", "user", "accountId", "id", "length", "userRoles", "map", "roleId", "userId", "usersRole", "data", "findOne", "findAll", "queries", "builder", "take", "pagination", "utilService", "getQueryBuilderAndPagination", "isEmpty", "search", "andWhere", "leftJoinAndSelect", "select", "result", "total", "getManyAndCount", "totalPages", "Math", "ceil", "totalRecords", "findOneUserWithAllRolesById", "getAllUserRoles", "assignRolesToUser", "findOneBy", "delete", "removeRoleFromUser", "update", "updateUserDto", "remove", "avatar", "mediaService", "constructor"], "mappings": "oGASaA,qDAAAA,qCAT6B,iDACV,uEACH,kDACa,skBAMnC,IAAA,AAAMA,YAAN,MAAMA,YAQT,MAAMC,OAAOC,aAA4B,CAAE,CACvC,KAAM,CAAEC,QAAQ,CAAEC,QAAQ,CAAEC,OAAO,CAAE,GAAGC,KAAM,CAAGJ,cACjD,KAAM,CAAEK,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACC,YAAY,CAACC,YAAY,CAACR,cAAcE,QAAQ,EAC5E,MAAMO,QAAU,MAAM,IAAI,CAACC,QAAQ,CAACD,OAAO,CAACE,IAAI,CAC5C,IAAI,CAACD,QAAQ,CAACD,OAAO,CAACV,MAAM,CAAC,CACzBE,SAAUD,cAAcC,QAAQ,CAChCC,SAAUI,KACVD,IACJ,IAGJ,GAAI,CAACI,QAAS,CACV,MAAM,IAAIG,qBAAa,CAAC,wBAAyB,IACrD,CAEA,MAAMC,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACF,IAAI,CAAC,IAAI,CAACD,QAAQ,CAACG,IAAI,CAACd,MAAM,CAAC,CAAE,GAAGK,IAAI,CAAEU,UAAWL,QAAQM,EAAE,AAAC,IACtG,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,qBAAsB,IAClD,CAGA,GAAIT,SAAWA,QAAQa,MAAM,CAAG,EAAG,CAC/B,MAAMC,UAAYd,QAAQe,GAAG,CAAC,AAACC,QAAY,CAAA,CACvCC,OAAQP,KAAKE,EAAE,CACfI,MACJ,CAAA,EACA,OAAM,IAAI,CAACT,QAAQ,CAACW,SAAS,CAACV,IAAI,CAACM,UACvC,CAEA,MAAO,CACHK,KAAM,CACFb,QACAI,KAAM,MAAM,IAAI,CAACU,OAAO,CAACV,KAAKE,EAAE,CACpC,CACJ,CACJ,CAEA,MAAMS,QAAQC,OAA0E,CAAE,CACtF,KAAM,CAAEC,OAAO,CAAEC,IAAI,CAAEC,UAAU,CAAE,CAAG,IAAI,CAACC,WAAW,CAACC,4BAA4B,CAAC,IAAI,CAACpB,QAAQ,CAACG,IAAI,CAAEY,SAExG,GAAI,CAAC,IAAI,CAACI,WAAW,CAACE,OAAO,CAACN,QAAQO,MAAM,EAAG,CAC3CN,QAAQO,QAAQ,CAAC,6DAA8D,CAAED,OAAQ,CAAC,CAAC,EAAEP,QAAQO,MAAM,CAAC,CAAC,CAAC,AAAC,EACnH,CAGAN,QAAQQ,iBAAiB,CAAC,cAAe,QACzCR,QAAQQ,iBAAiB,CAAC,mBAAoB,aAC9CR,QAAQQ,iBAAiB,CAAC,iBAAkB,YAC5CR,QAAQQ,iBAAiB,CAAC,gBAAiB,UAC3CR,QAAQS,MAAM,CAAC,CAAC,SAAU,UAAW,YAAa,YAAa,cAAe,gBAAiB,YAAY,EAE3G,KAAM,CAACC,OAAQC,MAAM,CAAG,MAAMX,QAAQY,eAAe,GACrD,MAAMC,WAAaC,KAAKC,IAAI,CAACJ,MAAQV,MACrC,MAAO,CACHL,KAAMc,OACNR,WAAY,CACR,GAAGA,UAAU,CACbc,aAAcL,MACdE,WAAYA,UAChB,CACJ,CACJ,CAEAhB,QAAQR,EAAU,CAAE,CAChB,OAAO,IAAI,CAACL,QAAQ,CAACG,IAAI,CAAC8B,2BAA2B,CAAC5B,GAC1D,CAGA,MAAM6B,gBAAgBxB,MAAc,CAAE,CAClC,OAAO,IAAI,CAACV,QAAQ,CAACG,IAAI,CAAC+B,eAAe,CAACxB,OAC9C,CAGA,MAAMyB,kBAAkBzB,MAAc,CAAEjB,OAAiB,CAAE,CACvD,MAAMU,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACiC,SAAS,CAAC,CAAE/B,GAAIK,MAAO,GAC7D,GAAI,CAACP,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAGA,MAAM,IAAI,CAACF,QAAQ,CAACW,SAAS,CAAC0B,MAAM,CAAC,CAAE3B,MAAO,GAG9C,MAAMH,UAAYd,QAAQe,GAAG,CAAC,AAACC,QAAY,CAAA,CACvCC,OACAD,MACJ,CAAA,EAEA,OAAM,IAAI,CAACT,QAAQ,CAACW,SAAS,CAACV,IAAI,CAACM,WAEnC,OAAO,IAAI,CAACM,OAAO,CAACH,OACxB,CAGA,MAAM4B,mBAAmB5B,MAAc,CAAED,MAAc,CAAE,CACrD,MAAMN,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACiC,SAAS,CAAC,CAAE/B,GAAIK,MAAO,GAC7D,GAAI,CAACP,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAEA,MAAM,IAAI,CAACF,QAAQ,CAACW,SAAS,CAAC0B,MAAM,CAAC,CAAE3B,OAAQD,MAAO,GAEtD,OAAO,IAAI,CAACI,OAAO,CAACH,OACxB,CAEA,MAAM6B,OAAOlC,EAAU,CAAEmC,aAA4B,CAAE,CACnD,KAAM,CAAEjD,QAAQ,CAAEC,QAAQ,CAAEC,OAAO,CAAE,GAAGC,KAAM,CAAG8C,cACjD,MAAMrC,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACiC,SAAS,CAAC,CAAE/B,EAAG,GACrD,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAEA,GAAIV,SAAU,CACV,KAAM,CAAEG,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACC,YAAY,CAACC,YAAY,CAAC0C,cAAchD,QAAQ,EAC5E,IAAI,CAACQ,QAAQ,CAACD,OAAO,CAACwC,MAAM,CAAC,CAAElC,GAAIF,KAAKC,SAAS,AAAC,EAAG,CAAEZ,SAAUI,KAAMD,IAAK,EAChF,CAGA,GAAIF,SAAWA,QAAQa,MAAM,CAAG,EAAG,CAE/B,MAAM,IAAI,CAACN,QAAQ,CAACW,SAAS,CAAC0B,MAAM,CAAC,CAAE3B,OAAQL,EAAG,GAGlD,MAAME,UAAYd,QAAQe,GAAG,CAAC,AAACC,QAAY,CAAA,CACvCC,OAAQL,GACRI,MACJ,CAAA,EACA,OAAM,IAAI,CAACT,QAAQ,CAACW,SAAS,CAACV,IAAI,CAACM,UACvC,CAEA,MAAM,IAAI,CAACP,QAAQ,CAACG,IAAI,CAACoC,MAAM,CAAC,CAAElC,EAAG,EAAGX,MACxC,OAAO,IAAI,CAACmB,OAAO,CAACR,GACxB,CAEA,MAAMoC,OAAOpC,EAAU,CAAE,CACrB,MAAMF,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACiC,SAAS,CAAC,CAAE/B,EAAG,GACrD,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAGA,MAAM,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACkC,MAAM,CAAC,CAAEhC,EAAG,EAErC,OAAM,IAAI,CAACL,QAAQ,CAACD,OAAO,CAACsC,MAAM,CAAC,CAAEhC,GAAIF,KAAKC,SAAS,AAAC,GAExD,GAAID,KAAKuC,MAAM,EAAErC,GAAI,CACjB,MAAM,IAAI,CAACsC,YAAY,CAACF,MAAM,CAACtC,KAAKuC,MAAM,CAACrC,EAAE,CACjD,CAEA,OAAO,IACX,CA7JAuC,YACI,AAAiB/C,YAA0B,CAC3C,AAAiBsB,WAAwB,CACzC,AAAiBwB,YAA0B,CAC3C,AAAiB3C,QAAyB,CAC5C,MAJmBH,aAAAA,kBACAsB,YAAAA,iBACAwB,aAAAA,kBACA3C,SAAAA,QAClB,CAyJP"}