{"version": 3, "sources": ["../../../src/modules/role/role.service.ts"], "sourcesContent": ["import { Injectable, NotFoundException, HttpException } from '@nestjs/common';\nimport { RoleRepository } from '~/database/typeorm/repositories/role.repository';\nimport { DatabaseService } from '~/database/typeorm/database.service';\nimport { UtilService } from '~/shared/services';\nimport { CreateRoleDto } from './dto/create-role.dto';\nimport { UpdateRoleDto } from './dto/update-role.dto';\n\n@Injectable()\nexport class RoleService {\n    constructor(\n        private readonly roleRepository: RoleRepository,\n        private readonly utilService: UtilService,\n        private readonly database: DatabaseService,\n    ) {}\n\n    async create(createRoleDto: CreateRoleDto) {\n        const entity = this.roleRepository.create({\n            name: createRoleDto.name,\n            description: createRoleDto.description,\n        });\n        const result = await this.roleRepository.save(entity);\n\n        // add permissions to role\n        if (createRoleDto.permissionIds?.length) {\n            this.roleRepository.createQueryBuilder().relation('permissions').of(result).add(createRoleDto.permissionIds);\n        }\n\n        return result;\n    }\n\n    async findAll(query: { page: number; perPage: number; sortBy: string; search: string }) {\n        const { take, skip, pagination } = this.utilService.getPagination(query);\n        const builder = this.roleRepository.createQueryBuilder('entity');\n\n        if (Number(query.perPage) !== 0) builder.take(take).skip(skip);\n        if (query.sortBy) builder.orderBy(this.utilService.getSortCondition('entity', query.sortBy));\n        if (query.search) builder.andWhere('entity.name ILIKE :name', { name: `%${query.search}%` });\n\n        const [result, total] = await builder.getManyAndCount();\n        const totalPages = Math.ceil(total / take);\n\n        return {\n            data: result,\n            pagination: {\n                ...pagination,\n                totalRecords: total,\n                totalPages: totalPages,\n            },\n        };\n    }\n\n    async findOne(id: number) {\n        const result = await this.roleRepository.findOne({ relations: ['permissions'], where: { id } });\n        if (!result) throw new NotFoundException('Không tìm thấy quyền này!');\n\n        const { permissions, ...rest } = result;\n\n        return {\n            ...rest,\n            permissionIds: permissions.length > 0 ? permissions.map((permission) => permission.id) : [],\n        };\n    }\n\n    async update(id: number, updateRoleDto: UpdateRoleDto) {\n        const { permissionIds, ...rest } = updateRoleDto;\n        const result = await this.roleRepository.update(id, rest);\n\n        // delete all permissions of role\n        await this.roleRepository.removePermissions(id);\n\n        // add permissions to role\n        if (permissionIds?.length) {\n            await this.roleRepository.createQueryBuilder().relation('permissions').of(id).add(permissionIds);\n        }\n\n        return result;\n    }\n\n    async remove(id: number) {\n        // Check if role is being used by users (both single role and multiple roles)\n        const usersWithSingleRole = await this.database.user.count({ where: { roleId: id } });\n        const usersWithMultipleRoles = await this.database.usersRole.count({ where: { roleId: id } });\n\n        if (usersWithSingleRole > 0 || usersWithMultipleRoles > 0) {\n            throw new HttpException(`Không thể xóa role này vì có ${usersWithSingleRole + usersWithMultipleRoles} người dùng đang sử dụng`, 400);\n        }\n\n        await this.roleRepository.removePermissions(id);\n        return this.roleRepository.delete(id);\n    }\n\n    // Get all users assigned to this role (both single and multiple)\n    async getUsersByRole(roleId: number) {\n        const role = await this.roleRepository.findOneBy({ id: roleId });\n        if (!role) {\n            throw new HttpException('Không tìm thấy role', 404);\n        }\n\n        // Get users with single role\n        const usersWithSingleRole = await this.database.user.find({\n            where: { roleId },\n            relations: ['role'],\n        });\n\n        // Get users with multiple roles\n        const userRoles = await this.database.usersRole.find({\n            where: { roleId },\n            relations: ['user', 'user.role'],\n        });\n\n        return {\n            role,\n            usersWithSingleRole,\n            usersWithMultipleRoles: userRoles.map((ur) => ur.user),\n            totalUsers: usersWithSingleRole.length + userRoles.length,\n        };\n    }\n\n    // Get role usage statistics\n    async getRoleUsageStats(roleId: number) {\n        const role = await this.roleRepository.findOneBy({ id: roleId });\n        if (!role) {\n            throw new HttpException('Không tìm thấy role', 404);\n        }\n\n        const singleRoleCount = await this.database.user.count({ where: { roleId } });\n        const multipleRoleCount = await this.database.usersRole.count({ where: { roleId } });\n\n        return {\n            roleId,\n            roleName: role.name,\n            usersWithSingleRole: singleRoleCount,\n            usersWithMultipleRoles: multipleRoleCount,\n            totalUsers: singleRoleCount + multipleRoleCount,\n        };\n    }\n\n    // Check if role can be safely deleted\n    async canDeleteRole(roleId: number): Promise<boolean> {\n        const singleRoleCount = await this.database.user.count({ where: { roleId } });\n        const multipleRoleCount = await this.database.usersRole.count({ where: { roleId } });\n\n        return singleRoleCount === 0 && multipleRoleCount === 0;\n    }\n}\n"], "names": ["RoleService", "create", "createRoleDto", "entity", "roleRepository", "name", "description", "result", "save", "permissionIds", "length", "createQueryBuilder", "relation", "of", "add", "findAll", "query", "take", "skip", "pagination", "utilService", "getPagination", "builder", "Number", "perPage", "sortBy", "orderBy", "getSortCondition", "search", "andWhere", "total", "getManyAndCount", "totalPages", "Math", "ceil", "data", "totalRecords", "findOne", "id", "relations", "where", "NotFoundException", "permissions", "rest", "map", "permission", "update", "updateRoleDto", "removePermissions", "remove", "usersWithSingleRole", "database", "user", "count", "roleId", "usersWithMultipleRoles", "usersRole", "HttpException", "delete", "getUsersByRole", "role", "findOneBy", "find", "userRoles", "ur", "totalUsers", "getRoleUsageStats", "singleRoleCount", "multipleRoleCount", "<PERSON><PERSON><PERSON>", "canDeleteRole", "constructor"], "mappings": "oGAQaA,qDAAAA,qCARgD,gDAC9B,sFACC,mEACJ,skBAKrB,IAAA,AAAMA,YAAN,MAAMA,YAOT,MAAMC,OAAOC,aAA4B,CAAE,CACvC,MAAMC,OAAS,IAAI,CAACC,cAAc,CAACH,MAAM,CAAC,CACtCI,KAAMH,cAAcG,IAAI,CACxBC,YAAaJ,cAAcI,WAAW,AAC1C,GACA,MAAMC,OAAS,MAAM,IAAI,CAACH,cAAc,CAACI,IAAI,CAACL,QAG9C,GAAID,cAAcO,aAAa,EAAEC,OAAQ,CACrC,IAAI,CAACN,cAAc,CAACO,kBAAkB,GAAGC,QAAQ,CAAC,eAAeC,EAAE,CAACN,QAAQO,GAAG,CAACZ,cAAcO,aAAa,CAC/G,CAEA,OAAOF,MACX,CAEA,MAAMQ,QAAQC,KAAwE,CAAE,CACpF,KAAM,CAAEC,IAAI,CAAEC,IAAI,CAAEC,UAAU,CAAE,CAAG,IAAI,CAACC,WAAW,CAACC,aAAa,CAACL,OAClE,MAAMM,QAAU,IAAI,CAAClB,cAAc,CAACO,kBAAkB,CAAC,UAEvD,GAAIY,OAAOP,MAAMQ,OAAO,IAAM,EAAGF,QAAQL,IAAI,CAACA,MAAMC,IAAI,CAACA,MACzD,GAAIF,MAAMS,MAAM,CAAEH,QAAQI,OAAO,CAAC,IAAI,CAACN,WAAW,CAACO,gBAAgB,CAAC,SAAUX,MAAMS,MAAM,GAC1F,GAAIT,MAAMY,MAAM,CAAEN,QAAQO,QAAQ,CAAC,0BAA2B,CAAExB,KAAM,CAAC,CAAC,EAAEW,MAAMY,MAAM,CAAC,CAAC,CAAC,AAAC,GAE1F,KAAM,CAACrB,OAAQuB,MAAM,CAAG,MAAMR,QAAQS,eAAe,GACrD,MAAMC,WAAaC,KAAKC,IAAI,CAACJ,MAAQb,MAErC,MAAO,CACHkB,KAAM5B,OACNY,WAAY,CACR,GAAGA,UAAU,CACbiB,aAAcN,MACdE,WAAYA,UAChB,CACJ,CACJ,CAEA,MAAMK,QAAQC,EAAU,CAAE,CACtB,MAAM/B,OAAS,MAAM,IAAI,CAACH,cAAc,CAACiC,OAAO,CAAC,CAAEE,UAAW,CAAC,cAAc,CAAEC,MAAO,CAAEF,EAAG,CAAE,GAC7F,GAAI,CAAC/B,OAAQ,MAAM,IAAIkC,yBAAiB,CAAC,6BAEzC,KAAM,CAAEC,WAAW,CAAE,GAAGC,KAAM,CAAGpC,OAEjC,MAAO,CACH,GAAGoC,IAAI,CACPlC,cAAeiC,YAAYhC,MAAM,CAAG,EAAIgC,YAAYE,GAAG,CAAC,AAACC,YAAeA,WAAWP,EAAE,EAAI,EAAE,AAC/F,CACJ,CAEA,MAAMQ,OAAOR,EAAU,CAAES,aAA4B,CAAE,CACnD,KAAM,CAAEtC,aAAa,CAAE,GAAGkC,KAAM,CAAGI,cACnC,MAAMxC,OAAS,MAAM,IAAI,CAACH,cAAc,CAAC0C,MAAM,CAACR,GAAIK,KAGpD,OAAM,IAAI,CAACvC,cAAc,CAAC4C,iBAAiB,CAACV,IAG5C,GAAI7B,eAAeC,OAAQ,CACvB,MAAM,IAAI,CAACN,cAAc,CAACO,kBAAkB,GAAGC,QAAQ,CAAC,eAAeC,EAAE,CAACyB,IAAIxB,GAAG,CAACL,cACtF,CAEA,OAAOF,MACX,CAEA,MAAM0C,OAAOX,EAAU,CAAE,CAErB,MAAMY,oBAAsB,MAAM,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,CAAEb,MAAO,CAAEc,OAAQhB,EAAG,CAAE,GACnF,MAAMiB,uBAAyB,MAAM,IAAI,CAACJ,QAAQ,CAACK,SAAS,CAACH,KAAK,CAAC,CAAEb,MAAO,CAAEc,OAAQhB,EAAG,CAAE,GAE3F,GAAIY,oBAAsB,GAAKK,uBAAyB,EAAG,CACvD,MAAM,IAAIE,qBAAa,CAAC,CAAC,4CAA6B,EAAEP,oBAAsBK,uBAAuB,2BAAwB,CAAC,CAAE,IACpI,CAEA,MAAM,IAAI,CAACnD,cAAc,CAAC4C,iBAAiB,CAACV,IAC5C,OAAO,IAAI,CAAClC,cAAc,CAACsD,MAAM,CAACpB,GACtC,CAGA,MAAMqB,eAAeL,MAAc,CAAE,CACjC,MAAMM,KAAO,MAAM,IAAI,CAACxD,cAAc,CAACyD,SAAS,CAAC,CAAEvB,GAAIgB,MAAO,GAC9D,GAAI,CAACM,KAAM,CACP,MAAM,IAAIH,qBAAa,CAAC,sBAAuB,IACnD,CAGA,MAAMP,oBAAsB,MAAM,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACU,IAAI,CAAC,CACtDtB,MAAO,CAAEc,MAAO,EAChBf,UAAW,CAAC,OAAO,AACvB,GAGA,MAAMwB,UAAY,MAAM,IAAI,CAACZ,QAAQ,CAACK,SAAS,CAACM,IAAI,CAAC,CACjDtB,MAAO,CAAEc,MAAO,EAChBf,UAAW,CAAC,OAAQ,YAAY,AACpC,GAEA,MAAO,CACHqB,KACAV,oBACAK,uBAAwBQ,UAAUnB,GAAG,CAAC,AAACoB,IAAOA,GAAGZ,IAAI,EACrDa,WAAYf,oBAAoBxC,MAAM,CAAGqD,UAAUrD,MAAM,AAC7D,CACJ,CAGA,MAAMwD,kBAAkBZ,MAAc,CAAE,CACpC,MAAMM,KAAO,MAAM,IAAI,CAACxD,cAAc,CAACyD,SAAS,CAAC,CAAEvB,GAAIgB,MAAO,GAC9D,GAAI,CAACM,KAAM,CACP,MAAM,IAAIH,qBAAa,CAAC,sBAAuB,IACnD,CAEA,MAAMU,gBAAkB,MAAM,IAAI,CAAChB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,CAAEb,MAAO,CAAEc,MAAO,CAAE,GAC3E,MAAMc,kBAAoB,MAAM,IAAI,CAACjB,QAAQ,CAACK,SAAS,CAACH,KAAK,CAAC,CAAEb,MAAO,CAAEc,MAAO,CAAE,GAElF,MAAO,CACHA,OACAe,SAAUT,KAAKvD,IAAI,CACnB6C,oBAAqBiB,gBACrBZ,uBAAwBa,kBACxBH,WAAYE,gBAAkBC,iBAClC,CACJ,CAGA,MAAME,cAAchB,MAAc,CAAoB,CAClD,MAAMa,gBAAkB,MAAM,IAAI,CAAChB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,CAAEb,MAAO,CAAEc,MAAO,CAAE,GAC3E,MAAMc,kBAAoB,MAAM,IAAI,CAACjB,QAAQ,CAACK,SAAS,CAACH,KAAK,CAAC,CAAEb,MAAO,CAAEc,MAAO,CAAE,GAElF,OAAOa,kBAAoB,GAAKC,oBAAsB,CAC1D,CAtIAG,YACI,AAAiBnE,cAA8B,CAC/C,AAAiBgB,WAAwB,CACzC,AAAiB+B,QAAyB,CAC5C,MAHmB/C,eAAAA,oBACAgB,YAAAA,iBACA+B,SAAAA,QAClB,CAmIP"}